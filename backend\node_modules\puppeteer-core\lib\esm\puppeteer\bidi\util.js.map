{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../../src/bidi/util.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH,OAAO,EAAC,YAAY,EAAE,UAAU,EAAC,MAAM,mBAAmB,CAAC;AAE3D,OAAO,EAAC,gBAAgB,EAAC,MAAM,mBAAmB,CAAC;AAGnD;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB,CACpC,MAAiB,EACjB,eAA4C;IAE5C,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;QAC5B,OAAO;IACT,CAAC;IACD,MAAM,MAAM,CAAC,UAAU;SACpB,IAAI,CAAC,eAAe,EAAE;QACrB,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,OAAO,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC;KAClC,CAAC;SACD,KAAK,CAAC,KAAK,CAAC,EAAE;QACb,sEAAsE;QACtE,iFAAiF;QACjF,UAAU,CAAC,KAAK,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,qBAAqB,CACnC,OAAqC;IAErC,IAAI,OAAO,CAAC,SAAS,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QACvC,OAAO,gBAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACzD,CAAC;IACD,MAAM,CAAC,IAAI,GAAG,EAAE,EAAE,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACvD,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;IACjC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;IAElB,mDAAmD;IACnD,MAAM,UAAU,GAAG,EAAE,CAAC;IACtB,IAAI,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;QACpE,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC;YAC5D,IACE,YAAY,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC;gBACtC,KAAK,CAAC,GAAG,KAAK,YAAY,CAAC,YAAY,EACvC,CAAC;gBACD,MAAM,GAAG,GAAG,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC1C,UAAU,CAAC,OAAO,CAChB,UAAU,KAAK,CAAC,YAAY,IAAI,GAAG,CAAC,YAAY,KAC9C,GAAG,CAAC,YACN,OAAO,GAAG,CAAC,UAAU,iBAAiB,KAAK,CAAC,UAAU,IACpD,KAAK,CAAC,YACR,GAAG,CACJ,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,UAAU,CAAC,IAAI,CACb,UAAU,KAAK,CAAC,YAAY,IAAI,aAAa,KAAK,KAAK,CAAC,GAAG,IACzD,KAAK,CAAC,UACR,IAAI,KAAK,CAAC,YAAY,GAAG,CAC1B,CAAC;YACJ,CAAC;YACD,IAAI,UAAU,CAAC,MAAM,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;gBAC/C,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,KAAK,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvD,OAAO,KAAK,CAAC;AACf,CAAC"}