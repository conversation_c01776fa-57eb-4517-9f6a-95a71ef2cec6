#!/bin/bash

# X Marketing Platform - Complete Automation Startup Script
# Starts all services with full automation capabilities

echo "🚀 Starting X Marketing Platform with Full Automation..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
print_status "Checking prerequisites..."

# Check PostgreSQL
if ! pg_isready -h localhost -p 5432 -U x_marketing_user > /dev/null 2>&1; then
    print_error "PostgreSQL is not running. Please start it first:"
    echo "   sudo systemctl start postgresql"
    exit 1
fi
print_success "PostgreSQL is running"

# Check Redis
if ! redis-cli ping > /dev/null 2>&1; then
    print_error "Redis is not running. Please start it first:"
    echo "   sudo systemctl start redis-server"
    exit 1
fi
print_success "Redis is running"

# Check Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed"
    exit 1
fi
print_success "Node.js is available"

# Check Python
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed"
    exit 1
fi
print_success "Python 3 is available"

# Check environment files
if [ ! -f "backend/.env.local" ]; then
    print_error "Backend environment file not found. Run setup first:"
    echo "   node scripts/setup-with-available-keys.js"
    exit 1
fi
print_success "Environment files found"

# Create logs directory
mkdir -p logs
print_success "Logs directory ready"

print_status "Starting services with full automation capabilities..."

# Start Backend API with X Automation
print_status "Starting Backend API with X Automation Service..."
cd backend
npm run dev > ../logs/backend.log 2>&1 &
BACKEND_PID=$!
cd ..
print_success "Backend API started (PID: $BACKEND_PID)"

# Wait for backend to be ready
print_status "Waiting for Backend API to be ready..."
for i in {1..30}; do
    if curl -s http://localhost:3001/health > /dev/null 2>&1; then
        print_success "Backend API is ready"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "Backend API failed to start"
        kill $BACKEND_PID 2>/dev/null
        exit 1
    fi
    sleep 2
done

# Start LLM Service with Enhanced Hugging Face Integration
print_status "Starting Enhanced LLM Service..."
cd llm-service
python app.py > ../logs/llm-service.log 2>&1 &
LLM_PID=$!
cd ..
print_success "LLM Service started (PID: $LLM_PID)"

# Wait for LLM service to be ready
print_status "Waiting for LLM Service to be ready..."
for i in {1..30}; do
    if curl -s http://localhost:3003/health > /dev/null 2>&1; then
        print_success "LLM Service is ready"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "LLM Service failed to start"
        kill $BACKEND_PID $LLM_PID 2>/dev/null
        exit 1
    fi
    sleep 2
done

# Start Comprehensive Telegram Bot
print_status "Starting Comprehensive Telegram Bot..."
cd telegram-bot
npm run dev > ../logs/telegram-bot.log 2>&1 &
TELEGRAM_PID=$!
cd ..
print_success "Telegram Bot started (PID: $TELEGRAM_PID)"

# Wait for Telegram bot to be ready
print_status "Waiting for Telegram Bot to be ready..."
for i in {1..20}; do
    if curl -s http://localhost:3002/health > /dev/null 2>&1; then
        print_success "Telegram Bot is ready"
        break
    fi
    if [ $i -eq 20 ]; then
        print_error "Telegram Bot failed to start"
        kill $BACKEND_PID $LLM_PID $TELEGRAM_PID 2>/dev/null
        exit 1
    fi
    sleep 2
done

# Start Frontend Dashboard
print_status "Starting Frontend Dashboard..."
cd frontend
npm run dev > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
cd ..
print_success "Frontend Dashboard started (PID: $FRONTEND_PID)"

# Wait for frontend to be ready
print_status "Waiting for Frontend to be ready..."
for i in {1..30}; do
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        print_success "Frontend is ready"
        break
    fi
    if [ $i -eq 30 ]; then
        print_warning "Frontend may still be starting up"
        break
    fi
    sleep 2
done

# Test API integrations
print_status "Testing API integrations..."

# Test Telegram Bot
TELEGRAM_TOKEN=$(grep TELEGRAM_BOT_TOKEN backend/.env.local | cut -d'=' -f2)
if curl -s "https://api.telegram.org/bot${TELEGRAM_TOKEN}/getMe" | grep -q '"ok":true'; then
    print_success "Telegram Bot API: Connected"
else
    print_warning "Telegram Bot API: Connection issue"
fi

# Test Hugging Face
HF_TOKEN=$(grep HUGGINGFACE_API_KEY backend/.env.local | cut -d'=' -f2)
if curl -s -H "Authorization: Bearer ${HF_TOKEN}" "https://api-inference.huggingface.co/models/gpt2" | grep -v error > /dev/null; then
    print_success "Hugging Face API: Connected"
else
    print_warning "Hugging Face API: Connection issue"
fi

# Display startup summary
echo ""
echo -e "${CYAN}🎉 X Marketing Platform Started Successfully!${NC}"
echo -e "${CYAN}=============================================${NC}"
echo ""
echo -e "${GREEN}📊 Available Services:${NC}"
echo -e "   • Frontend Dashboard: ${BLUE}http://localhost:3000${NC}"
echo -e "   • Backend API: ${BLUE}http://localhost:3001${NC}"
echo -e "   • API Documentation: ${BLUE}http://localhost:3001/api-docs${NC}"
echo -e "   • LLM Service: ${BLUE}http://localhost:3003${NC}"
echo -e "   • Telegram Bot: ${GREEN}Active and listening${NC}"
echo ""
echo -e "${GREEN}🚀 Automation Features:${NC}"
echo -e "   • AI Content Generation: ${GREEN}✅ Enabled${NC}"
echo -e "   • Automated X Posting: ${GREEN}✅ Enabled${NC}"
echo -e "   • Quality Control: ${GREEN}✅ Active${NC}"
echo -e "   • Compliance Monitoring: ${GREEN}✅ Active${NC}"
echo -e "   • Real-time Analytics: ${GREEN}✅ Active${NC}"
echo ""
echo -e "${GREEN}🎯 Available API Keys:${NC}"
echo -e "   • Telegram Bot: ${GREEN}✅ Configured${NC}"
echo -e "   • Hugging Face: ${GREEN}✅ Configured${NC}"
echo -e "   • X/Twitter API: ${GREEN}✅ Ready for automation${NC}"
echo ""
echo -e "${GREEN}🎨 Content Creation:${NC}"
echo -e "   • Multiple AI Models: ${GREEN}✅ Available${NC}"
echo -e "   • Image Generation: ${GREEN}✅ Available${NC}"
echo -e "   • Sentiment Analysis: ${GREEN}✅ Available${NC}"
echo -e "   • Quality Scoring: ${GREEN}✅ Available${NC}"
echo ""
echo -e "${GREEN}🤖 Automation Controls:${NC}"
echo -e "   • Smart Scheduling: ${GREEN}✅ Active${NC}"
echo -e "   • Rate Limiting: ${GREEN}✅ Active${NC}"
echo -e "   • Human-like Patterns: ${GREEN}✅ Active${NC}"
echo -e "   • Emergency Stop: ${GREEN}✅ Available${NC}"
echo ""
echo -e "${YELLOW}📱 Telegram Bot Usage:${NC}"
echo -e "   1. Message your bot: ${BLUE}@YourBotUsername${NC}"
echo -e "   2. Send: ${BLUE}/start${NC} to begin"
echo -e "   3. Use: ${BLUE}/help${NC} for all commands"
echo -e "   4. Try: ${BLUE}/generate Bitcoin analysis${NC}"
echo -e "   5. Control: ${BLUE}/automation${NC} for automation"
echo ""
echo -e "${YELLOW}🌐 Web Dashboard:${NC}"
echo -e "   1. Open: ${BLUE}http://localhost:3000${NC}"
echo -e "   2. Register/Login to your account"
echo -e "   3. Add your X accounts"
echo -e "   4. Configure automation settings"
echo -e "   5. Monitor real-time analytics"
echo ""
echo -e "${YELLOW}🔧 Browser Assistant:${NC}"
echo -e "   1. Install extension from: ${BLUE}browser-assistant/${NC}"
echo -e "   2. Navigate to X/Twitter"
echo -e "   3. Use the ${BLUE}🚀 Assistant${NC} button"
echo -e "   4. Generate and insert content"
echo ""
echo -e "${PURPLE}💡 Pro Tips:${NC}"
echo -e "   • Start with ${BLUE}/generate <topic>${NC} for AI content"
echo -e "   • Use ${BLUE}/automation${NC} for full automation control"
echo -e "   • Monitor with ${BLUE}/dashboard${NC} for real-time stats"
echo -e "   • Emergency stop with ${BLUE}/emergency_stop${NC}"
echo -e "   • Quality content with built-in compliance"
echo ""
echo -e "${RED}⚠️  Important Notes:${NC}"
echo -e "   • All automation includes quality controls"
echo -e "   • Content is automatically compliance-checked"
echo -e "   • Human-like posting patterns are enforced"
echo -e "   • Rate limits are automatically respected"
echo -e "   • Emergency stop available at any time"
echo ""
echo -e "${GREEN}📊 Process IDs (for manual control):${NC}"
echo -e "   • Backend: ${BACKEND_PID}"
echo -e "   • LLM Service: ${LLM_PID}"
echo -e "   • Telegram Bot: ${TELEGRAM_PID}"
echo -e "   • Frontend: ${FRONTEND_PID}"
echo ""
echo -e "${CYAN}Press Ctrl+C to stop all services${NC}"
echo ""

# Save PIDs for cleanup
echo "$BACKEND_PID $LLM_PID $TELEGRAM_PID $FRONTEND_PID" > .platform_pids

# Function to cleanup on exit
cleanup() {
    echo ""
    print_status "Stopping all services..."
    
    if [ -f .platform_pids ]; then
        PIDS=$(cat .platform_pids)
        for PID in $PIDS; do
            if kill -0 $PID 2>/dev/null; then
                kill $PID
                print_success "Stopped process $PID"
            fi
        done
        rm .platform_pids
    fi
    
    print_success "All services stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Wait for interrupt
print_status "Platform is running. Press Ctrl+C to stop all services."
wait
