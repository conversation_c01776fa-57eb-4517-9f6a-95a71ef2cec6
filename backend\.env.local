# Database
DATABASE_URL=postgresql://x_marketing_user:secure_password_123@localhost:5432/x_marketing_platform

# Redis
REDIS_URL=redis://localhost:6379

# Security
JWT_SECRET=2598db2abba8d2c149fe165bce1a7540815d86c35e5da20017c5f2f831436dec
JWT_REFRESH_SECRET=8862ff58452c58cb35cfb7ac85f5c253c9d97ed5a84add57e4b6fc94ce63b70f
ENCRYPTION_KEY=7986bad3891be78d439143afc3ad2d6b2f402046775ebe445c5ac4c429264e75

# X API (Available for Regional Automation)
X_API_KEY=your-x-api-key-here
X_API_SECRET=your-x-api-secret-here
X_BEARER_TOKEN=your-x-bearer-token-here
X_ACCESS_TOKEN=your-x-access-token-here
X_ACCESS_TOKEN_SECRET=your-x-access-token-secret-here

# Telegram (Available)
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_WEBHOOK_URL=http://localhost:3002/webhook

# LLM Services (Available)
OLLAMA_HOST=http://localhost:11434
HUGGINGFACE_API_KEY=*************************************

# Application URLs
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:3001
LLM_SERVICE_URL=http://localhost:3003
TELEGRAM_BOT_URL=http://localhost:3002

# Environment
NODE_ENV=development
PORT=3001
LOG_LEVEL=debug

# Features (Full Automation Mode)
ENABLE_ADVANCED_FEATURES=true
ENABLE_HUGGINGFACE_INTEGRATION=true
ENABLE_BROWSER_ASSISTANT=true
ENABLE_CONTENT_GENERATION=true
ENABLE_X_AUTOMATION=true
ENABLE_AUTOMATED_POSTING=true
COMPLIANCE_STRICT_MODE=true

# Automation Mode Settings
CONTENT_CREATION_MODE=false
MANUAL_POSTING_MODE=false
AUTOMATION_MODE=true
X_API_AVAILABLE=true
REGIONAL_AUTOMATION_PERMITTED=true

# Performance (Development)
MAX_ACCOUNTS_PER_USER=50
MAX_DAILY_ACTIONS_PER_ACCOUNT=200
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000