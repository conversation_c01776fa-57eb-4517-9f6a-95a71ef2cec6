# X Marketing Platform - PowerShell Deployment Script
# Complete automation deployment with comprehensive testing

param(
    [switch]$SkipDependencyCheck,
    [switch]$QuickStart,
    [string]$LogLevel = "Info"
)

# Color functions for better output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    switch ($Color) {
        "Red" { Write-Host $Message -ForegroundColor Red }
        "Green" { Write-Host $Message -ForegroundColor Green }
        "Yellow" { Write-Host $Message -ForegroundColor Yellow }
        "Blue" { Write-Host $Message -ForegroundColor Blue }
        "Cyan" { Write-Host $Message -ForegroundColor Cyan }
        "Magenta" { Write-Host $Message -ForegroundColor Magenta }
        default { Write-Host $Message }
    }
}

function Write-Status {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Blue"
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "[SUCCESS] $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "[WARNING] $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

# Main deployment function
function Deploy-XMarketingPlatform {
    Write-ColorOutput "🚀 X Marketing Platform - Complete Automation Suite Deployment" "Cyan"
    Write-ColorOutput "=================================================================" "Cyan"
    Write-Host ""

    # Check prerequisites
    if (-not $SkipDependencyCheck) {
        Write-Status "Checking system prerequisites..."
        
        # Check Node.js
        try {
            $nodeVersion = node --version 2>$null
            if ($nodeVersion) {
                Write-Success "Node.js is available: $nodeVersion"
            } else {
                throw "Node.js not found"
            }
        } catch {
            Write-Error "Node.js is not installed or not in PATH"
            Write-Host "Please install Node.js from https://nodejs.org/"
            Write-Host "After installation, restart PowerShell and run this script again"
            return $false
        }

        # Check Python
        try {
            $pythonVersion = python --version 2>$null
            if ($pythonVersion) {
                Write-Success "Python is available: $pythonVersion"
            } else {
                Write-Warning "Python not found - some features may be limited"
            }
        } catch {
            Write-Warning "Python not available - LLM service may not work"
        }

        # Check Git
        try {
            $gitVersion = git --version 2>$null
            if ($gitVersion) {
                Write-Success "Git is available: $gitVersion"
            }
        } catch {
            Write-Warning "Git not found - manual file management required"
        }
    }

    # Create logs directory
    if (-not (Test-Path "logs")) {
        New-Item -ItemType Directory -Path "logs" | Out-Null
        Write-Success "Created logs directory"
    }

    # Setup environment configuration
    Write-Status "Setting up environment configuration..."
    if (-not (Test-Path "backend\.env.local")) {
        Write-Status "Running environment setup..."
        try {
            node scripts\setup-with-available-keys.js
            Write-Success "Environment configuration completed"
        } catch {
            Write-Error "Environment setup failed: $_"
            return $false
        }
    } else {
        Write-Success "Environment configuration already exists"
    }

    # Validate API credentials
    Write-Status "Validating API credentials..."
    $envContent = Get-Content "backend\.env.local" -Raw
    
    if ($envContent -match "TELEGRAM_BOT_TOKEN=**********:AAFm6v8KPzn1zPZmHKklXjkIwzQ8fYY25O0") {
        Write-Success "✅ Telegram Bot Token: Configured"
    } else {
        Write-Warning "⚠️ Telegram Bot Token: Not properly configured"
    }
    
    if ($envContent -match "HUGGINGFACE_API_KEY=*************************************") {
        Write-Success "✅ Hugging Face API Key: Configured"
    } else {
        Write-Warning "⚠️ Hugging Face API Key: Not properly configured"
    }

    # Setup backend
    Write-Status "Setting up backend with complete automation suite..."
    Set-Location "backend"
    
    if (-not (Test-Path "node_modules")) {
        Write-Status "Installing backend dependencies..."
        npm install
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Backend dependency installation failed"
            Set-Location ".."
            return $false
        }
    }

    # Setup database
    if (Test-Path "prisma\schema.prisma") {
        Write-Status "Setting up database..."
        npx prisma generate
        npx prisma db push
    }
    
    Set-Location ".."
    Write-Success "Backend setup complete"

    # Setup LLM service
    Write-Status "Setting up enhanced LLM service..."
    Set-Location "llm-service"
    
    if (-not (Test-Path "venv")) {
        Write-Status "Creating Python virtual environment..."
        try {
            python -m venv venv
            Write-Success "Virtual environment created"
        } catch {
            Write-Warning "Failed to create virtual environment - Python features may be limited"
            Set-Location ".."
            goto SkipLLM
        }
    }

    try {
        Write-Status "Installing LLM service dependencies..."
        & "venv\Scripts\Activate.ps1"
        pip install -r requirements.txt
        Write-Success "LLM service dependencies installed"
    } catch {
        Write-Warning "LLM service setup had issues - some features may be limited"
    }
    
    Set-Location ".."

    :SkipLLM
    # Setup Telegram bot
    Write-Status "Setting up comprehensive Telegram bot..."
    Set-Location "telegram-bot"
    
    if (-not (Test-Path "node_modules")) {
        Write-Status "Installing Telegram bot dependencies..."
        npm install
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Telegram bot dependency installation failed"
            Set-Location ".."
            return $false
        }
    }
    
    Set-Location ".."
    Write-Success "Telegram bot setup complete"

    # Setup frontend (optional)
    if (Test-Path "frontend") {
        Write-Status "Setting up frontend dashboard..."
        Set-Location "frontend"
        
        if (-not (Test-Path "node_modules")) {
            npm install
            if ($LASTEXITCODE -ne 0) {
                Write-Warning "Frontend setup had issues"
            }
        }
        
        Set-Location ".."
        Write-Success "Frontend setup complete"
    }

    return $true
}

# Start services function
function Start-Services {
    Write-ColorOutput "🎯 Starting All Services..." "Cyan"
    Write-Host ""

    # Start Backend API
    Write-Status "Starting Backend API with complete automation..."
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD\backend'; npm run dev" -WindowStyle Normal
    Start-Sleep -Seconds 3
    Write-Success "Backend API started"

    # Start LLM Service
    if (Test-Path "llm-service\venv") {
        Write-Status "Starting enhanced LLM service..."
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD\llm-service'; .\venv\Scripts\Activate.ps1; python app.py" -WindowStyle Normal
        Start-Sleep -Seconds 3
        Write-Success "LLM Service started"
    } else {
        Write-Warning "LLM Service not available"
    }

    # Start Telegram Bot
    Write-Status "Starting comprehensive Telegram bot..."
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD\telegram-bot'; npm run dev" -WindowStyle Normal
    Start-Sleep -Seconds 3
    Write-Success "Telegram Bot started"

    # Start Frontend
    if (Test-Path "frontend") {
        Write-Status "Starting frontend dashboard..."
        Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD\frontend'; npm run dev" -WindowStyle Normal
        Start-Sleep -Seconds 2
        Write-Success "Frontend started"
    }
}

# Test services function
function Test-Services {
    Write-Status "Testing services..."
    Start-Sleep -Seconds 10  # Wait for services to start

    # Test Backend
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3001/health" -TimeoutSec 5 -ErrorAction Stop
        Write-Success "✅ Backend API: Responding"
    } catch {
        Write-Warning "⚠️ Backend API: Not responding yet"
    }

    # Test LLM Service
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3003/health" -TimeoutSec 5 -ErrorAction Stop
        Write-Success "✅ LLM Service: Responding"
    } catch {
        Write-Warning "⚠️ LLM Service: Not responding yet"
    }

    # Test Telegram Bot
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3002/health" -TimeoutSec 5 -ErrorAction Stop
        Write-Success "✅ Telegram Bot: Responding"
    } catch {
        Write-Warning "⚠️ Telegram Bot: Not responding yet"
    }
}

# Display final status
function Show-DeploymentStatus {
    Write-Host ""
    Write-ColorOutput "🎉 X Marketing Platform - Complete Automation Suite Deployed!" "Cyan"
    Write-ColorOutput "=================================================================" "Cyan"
    Write-Host ""
    
    Write-ColorOutput "🌐 Available Services:" "Green"
    Write-Host "   • Backend API: http://localhost:3001"
    Write-Host "   • API Documentation: http://localhost:3001/api-docs"
    Write-Host "   • LLM Service: http://localhost:3003"
    Write-Host "   • Telegram Bot: Active and listening"
    if (Test-Path "frontend") {
        Write-Host "   • Frontend Dashboard: http://localhost:3000"
    }
    Write-Host ""
    
    Write-ColorOutput "🤖 Complete Automation Suite:" "Green"
    Write-Host "   • AI Content Generation: ✅ Active"
    Write-Host "   • Automated Posting: ✅ Active"
    Write-Host "   • Automated Liking: ✅ Active"
    Write-Host "   • Automated Commenting: ✅ Active"
    Write-Host "   • Automated Retweeting: ✅ Active"
    Write-Host "   • Automated Following: ✅ Active"
    Write-Host "   • Automated DM: ✅ Active"
    Write-Host "   • Poll Voting: ✅ Active"
    Write-Host "   • Thread Management: ✅ Active"
    Write-Host "   • Quality Control: ✅ Active"
    Write-Host "   • Compliance Monitoring: ✅ Active"
    Write-Host "   • Multi-Account Management: ✅ Active"
    Write-Host ""
    
    Write-ColorOutput "🔑 API Credentials:" "Green"
    Write-Host "   • Telegram Bot: ✅ **********:AAFm6v8KPzn1zPZmHKklXjkIwzQ8fYY25O0"
    Write-Host "   • Hugging Face: ✅ *************************************"
    Write-Host "   • X/Twitter API: ⚠️ Add your credentials to environment files"
    Write-Host ""
    
    Write-ColorOutput "📱 Comprehensive Telegram Bot Control:" "Yellow"
    Write-Host "   1. Message your bot on Telegram"
    Write-Host "   2. Send: /start to initialize"
    Write-Host "   3. Use: /help for all 50+ commands"
    Write-Host "   4. Content: /generate <topic> for AI content"
    Write-Host "   5. Automation: /automation for full control"
    Write-Host "   6. Liking: /like_automation for like automation"
    Write-Host "   7. Commenting: /comment_automation for comment automation"
    Write-Host "   8. Following: /follow_automation for follow automation"
    Write-Host "   9. Analytics: /dashboard for real-time analytics"
    Write-Host "   10. Emergency: /emergency_stop for immediate halt"
    Write-Host ""
    
    Write-ColorOutput "🎯 Next Steps:" "Yellow"
    Write-Host "   1. Add your X/Twitter API credentials to the environment files"
    Write-Host "   2. Message your Telegram bot to start using the platform"
    Write-Host "   3. Use the web dashboard at http://localhost:3000"
    Write-Host "   4. Monitor the service windows for logs and status"
    Write-Host ""
    
    Write-ColorOutput "✅ All services are running in separate PowerShell windows" "Green"
    Write-ColorOutput "✅ Platform is ready for complete X/Twitter automation" "Green"
    Write-Host ""
}

# Main execution
try {
    $deployResult = Deploy-XMarketingPlatform
    
    if ($deployResult) {
        Start-Services
        Test-Services
        Show-DeploymentStatus
        
        # Open web dashboard
        Write-Status "Opening web dashboard..."
        Start-Process "http://localhost:3000"
        
        Write-ColorOutput "🎯 Deployment Complete! Your X Marketing Platform is ready!" "Green"
        Write-Host ""
        Write-Host "Press any key to continue..."
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    } else {
        Write-Error "Deployment failed. Please check the errors above and try again."
    }
} catch {
    Write-Error "Deployment script failed: $_"
}
