# path-type [![Build Status](https://travis-ci.org/sindresorhus/path-type.svg?branch=master)](https://travis-ci.org/sindresorhus/path-type)

> Check if a path is a file, directory, or symlink


## Install

```
$ npm install path-type
```


## Usage

```js
const {isFile} = require('path-type');

(async () => {
	console.log(await isFile('package.json'));
	//=> true
})();
```


## API

### isFile(path)

Check whether the passed `path` is a file.

Returns a `Promise<boolean>`.

#### path

Type: `string`

The path to check.

### isDirectory(path)

Check whether the passed `path` is a directory.

Returns a `Promise<boolean>`.

### isSymlink(path)

Check whether the passed `path` is a symlink.

Returns a `Promise<boolean>`.

### isFileSync(path)

Synchronously check whether the passed `path` is a file.

Returns a `boolean`.

### isDirectorySync(path)

Synchronously check whether the passed `path` is a directory.

Returns a `boolean`.

### isSymlinkSync(path)

Synchronously check whether the passed `path` is a symlink.

Returns a `boolean`.


## License

MIT © [Sindre Sorhus](https://sindresorhus.com)
