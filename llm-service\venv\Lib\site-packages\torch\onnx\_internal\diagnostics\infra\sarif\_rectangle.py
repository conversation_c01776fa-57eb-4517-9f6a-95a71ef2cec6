# DO NOT EDIT! This file was generated by jschema_to_python version 0.0.1.dev29,
# with extension for dataclasses and type annotation.

from __future__ import annotations

import dataclasses
from typing import Optional

from torch.onnx._internal.diagnostics.infra.sarif import _message, _property_bag


@dataclasses.dataclass
class Rectangle(object):
    """An area within an image."""

    bottom: Optional[float] = dataclasses.field(
        default=None, metadata={"schema_property_name": "bottom"}
    )
    left: Optional[float] = dataclasses.field(
        default=None, metadata={"schema_property_name": "left"}
    )
    message: Optional[_message.Message] = dataclasses.field(
        default=None, metadata={"schema_property_name": "message"}
    )
    properties: Optional[_property_bag.PropertyBag] = dataclasses.field(
        default=None, metadata={"schema_property_name": "properties"}
    )
    right: Optional[float] = dataclasses.field(
        default=None, metadata={"schema_property_name": "right"}
    )
    top: Optional[float] = dataclasses.field(
        default=None, metadata={"schema_property_name": "top"}
    )


# flake8: noqa
