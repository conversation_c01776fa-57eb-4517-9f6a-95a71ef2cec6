{"name": "x-marketing-telegram-bot", "version": "1.0.0", "description": "Telegram bot interface for X Marketing Platform", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"node-telegram-bot-api": "^0.64.0", "axios": "^1.6.2", "dotenv": "^16.3.1", "winston": "^3.11.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "joi": "^17.11.0", "cron": "^3.1.6", "redis": "^4.6.10", "crypto-js": "^4.2.0", "moment": "^2.29.4", "chart.js": "^4.4.0", "chartjs-to-image": "^1.0.9", "canvas": "^2.11.2", "qrcode": "^1.5.3", "uuid": "^9.0.1", "@types/uuid": "^9.0.7"}, "devDependencies": {"@types/node": "^20.10.0", "@types/node-telegram-bot-api": "^0.64.7", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/qrcode": "^1.5.5", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^5.3.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"]}}