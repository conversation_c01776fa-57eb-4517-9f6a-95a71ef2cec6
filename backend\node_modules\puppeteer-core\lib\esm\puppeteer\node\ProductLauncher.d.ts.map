{"version": 3, "file": "ProductLauncher.d.ts", "sourceRoot": "", "sources": ["../../../../src/node/ProductLauncher.ts"], "names": [], "mappings": "AASA,OAAO,EAGL,MAAM,EAIP,MAAM,qBAAqB,CAAC;AAS7B,OAAO,KAAK,EAAC,OAAO,EAAE,oBAAoB,EAAC,MAAM,mBAAmB,CAAC;AAErE,OAAO,EAAC,UAAU,EAAC,MAAM,sBAAsB,CAAC;AAEhD,OAAO,KAAK,EAAC,OAAO,EAAC,MAAM,sBAAsB,CAAC;AAElD,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,uBAAuB,CAAC;AAEpD,OAAO,KAAK,EACV,4BAA4B,EAC5B,oBAAoB,EACpB,0BAA0B,EAC3B,MAAM,oBAAoB,CAAC;AAG5B,OAAO,KAAK,EAAC,aAAa,EAAC,MAAM,oBAAoB,CAAC;AAEtD;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC,iBAAiB,EAAE,OAAO,CAAC;IAC3B,WAAW,EAAE,MAAM,CAAC;IACpB,cAAc,EAAE,MAAM,CAAC;IACvB,IAAI,EAAE,MAAM,EAAE,CAAC;CAChB;AAED;;;;GAIG;AACH,8BAAsB,eAAe;;IAGnC;;OAEG;IACH,SAAS,EAAE,aAAa,CAAC;IAEzB;;OAEG;IACH,SAAS,CAAC,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAEzC;;OAEG;gBACS,SAAS,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO;IAKtD,IAAI,OAAO,IAAI,OAAO,CAErB;IAEK,MAAM,CAAC,OAAO,GAAE,0BAA+B,GAAG,OAAO,CAAC,OAAO,CAAC;IAsHxE,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,oBAAoB,GAAG,MAAM;IAE/D,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,4BAA4B,GAAG,MAAM,EAAE;IAEpE;;;;OAIG;IACH,wBAAwB,IAAI,MAAM,GAAG,SAAS;IAI9C;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,sBAAsB,CACvC,OAAO,EAAE,0BAA0B,GAClC,OAAO,CAAC,kBAAkB,CAAC;IAE9B;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CACjC,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE;QAAC,MAAM,EAAE,OAAO,CAAA;KAAC,GACtB,OAAO,CAAC,IAAI,CAAC;IAEhB;;OAEG;cACa,YAAY,CAC1B,cAAc,EAAE,UAAU,CAAC,OAAO,MAAM,CAAC,EACzC,aAAa,CAAC,EAAE,UAAU,GACzB,OAAO,CAAC,IAAI,CAAC;IAyBhB;;OAEG;cACa,iBAAiB,CAC/B,OAAO,EAAE,OAAO,EAChB,OAAO,EAAE,MAAM,GACd,OAAO,CAAC,IAAI,CAAC;IAchB;;OAEG;cACa,yBAAyB,CACvC,cAAc,EAAE,UAAU,CAAC,OAAO,MAAM,CAAC,EACzC,IAAI,EAAE;QAAC,OAAO,EAAE,MAAM,CAAC;QAAC,eAAe,EAAE,MAAM,GAAG,SAAS,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAC,GAC3E,OAAO,CAAC,UAAU,CAAC;IActB;;OAEG;cACa,uBAAuB,CACrC,cAAc,EAAE,UAAU,CAAC,OAAO,MAAM,CAAC,EACzC,IAAI,EAAE;QAAC,OAAO,EAAE,MAAM,CAAC;QAAC,eAAe,EAAE,MAAM,GAAG,SAAS,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAC,GAC3E,OAAO,CAAC,UAAU,CAAC;IAWtB;;OAEG;cACa,wBAAwB,CACtC,cAAc,EAAE,UAAU,CAAC,OAAO,MAAM,CAAC,EACzC,UAAU,EAAE,UAAU,EACtB,aAAa,EAAE,oBAAoB,EACnC,IAAI,EAAE;QACJ,OAAO,EAAE,MAAM,CAAC;QAChB,eAAe,EAAE,MAAM,GAAG,SAAS,CAAC;QACpC,MAAM,EAAE,MAAM,CAAC;QACf,eAAe,EAAE,QAAQ,GAAG,IAAI,CAAC;QACjC,iBAAiB,CAAC,EAAE,OAAO,CAAC;KAC7B,GACA,OAAO,CAAC,OAAO,CAAC;IAenB;;OAEG;cACa,iBAAiB,CAC/B,cAAc,EAAE,UAAU,CAAC,OAAO,MAAM,CAAC,EACzC,aAAa,EAAE,oBAAoB,EACnC,IAAI,EAAE;QACJ,OAAO,EAAE,MAAM,CAAC;QAChB,eAAe,EAAE,MAAM,GAAG,SAAS,CAAC;QACpC,MAAM,EAAE,MAAM,CAAC;QACf,eAAe,EAAE,QAAQ,GAAG,IAAI,CAAC;QACjC,iBAAiB,CAAC,EAAE,OAAO,CAAC;KAC7B,GACA,OAAO,CAAC,OAAO,CAAC;IAwBnB;;OAEG;IACH,SAAS,CAAC,cAAc,IAAI,MAAM;IAOlC;;OAEG;IACH,SAAS,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,OAAO,GAAG,KAAK,GAAG,MAAM;CAuDpE"}