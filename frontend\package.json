{"name": "x-marketing-frontend", "version": "1.0.0", "description": "Frontend dashboard for X Marketing Platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "typescript": "^5.3.0", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-query": "^3.39.3", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "date-fns": "^2.30.0", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "react-beautiful-dnd": "^13.1.1", "@types/react-beautiful-dnd": "^13.1.8", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "@types/react-syntax-highlighter": "^15.5.11", "lucide-react": "^0.294.0", "react-dropzone": "^14.2.3", "react-select": "^5.8.0", "react-datepicker": "^4.25.0", "@types/react-datepicker": "^4.19.4", "react-table": "^7.8.0", "@types/react-table": "^7.7.18", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "@types/react-window": "^1.8.8"}, "devDependencies": {"eslint": "^8.54.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "jest-environment-jsdom": "^29.7.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}