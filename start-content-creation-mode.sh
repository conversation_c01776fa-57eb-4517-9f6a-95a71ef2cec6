#!/bin/bash

# X Marketing Platform - Content Creation Mode Startup Script

echo "🚀 Starting X Marketing Platform in Full Automation Mode..."

# Check if all services are ready
echo "📋 Checking prerequisites..."

# Check PostgreSQL
if ! pg_isready -h localhost -p 5432 -U x_marketing_user > /dev/null 2>&1; then
    echo "❌ PostgreSQL is not running. Please start it first."
    echo "   sudo systemctl start postgresql"
    exit 1
fi

# Check Redis
if ! redis-cli ping > /dev/null 2>&1; then
    echo "❌ Redis is not running. Please start it first."
    echo "   sudo systemctl start redis-server"
    exit 1
fi

echo "✅ Prerequisites check passed"

# Start services in Content Creation Mode
echo "🎨 Starting Content Creation Mode services..."

# Start Backend API
echo "Starting Backend API..."
cd backend && npm run dev &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 3

# Start LLM Service with Hugging Face integration
echo "Starting LLM Service with Hugging Face..."
cd ../llm-service && python app.py &
LLM_PID=$!

# Wait a moment for LLM service to start
sleep 3

# Start Telegram Bot
echo "Starting Telegram Bot..."
cd ../telegram-bot && npm run dev &
TELEGRAM_PID=$!

# Wait a moment for telegram bot to start
sleep 3

# Start Frontend
echo "Starting Frontend Dashboard..."
cd ../frontend && npm run dev &
FRONTEND_PID=$!

echo ""
echo "🎉 X Marketing Platform started in Full Automation Mode!"
echo ""
echo "📊 Available Services:"
echo "   • Frontend Dashboard: http://localhost:3000"
echo "   • Backend API: http://localhost:3001"
echo "   • LLM Service: http://localhost:3003"
echo "   • Telegram Bot: Active and listening"
echo ""
echo "🎨 Content Creation Features:"
echo "   • AI-powered content generation (Hugging Face)"
echo "   • Sentiment analysis and optimization"
echo "   • Browser assistant for manual posting"
echo "   • Telegram bot for notifications and control"
echo "   • Image generation capabilities"
echo ""
echo "📝 Usage:"
echo "   1. Open http://localhost:3000 for the dashboard"
echo "   2. Generate content using AI tools"
echo "   3. Use browser assistant for posting"
echo "   4. Control via Telegram bot"
echo ""
echo "⚠️  Note: X/Twitter API not available due to regional restrictions"
echo "   Platform operates in manual posting mode with content assistance"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for interrupt
trap 'echo "Stopping services..."; kill $BACKEND_PID $LLM_PID $TELEGRAM_PID $FRONTEND_PID; exit' INT
wait
