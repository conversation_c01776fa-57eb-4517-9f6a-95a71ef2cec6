@echo off
REM X Marketing Platform - Windows Deployment Script
REM Complete automation deployment for Windows systems

echo 🚀 Starting X Marketing Platform - Complete Automation Suite...
echo.

REM Colors for Windows (limited support)
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "NC=[0m"

echo %CYAN%🎯 X Marketing Platform - Complete Automation Deployment%NC%
echo %CYAN%============================================================%NC%
echo.

REM Check if Node.js is installed
echo %BLUE%[INFO]%NC% Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%[ERROR]%NC% Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    echo After installation, restart this script
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo %GREEN%[SUCCESS]%NC% Node.js is available: %NODE_VERSION%

REM Check if Python is installed
echo %BLUE%[INFO]%NC% Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %YELLOW%[WARNING]%NC% Python is not installed or not in PATH
    echo Please install Python from https://python.org/
    echo Some features may not work without Python
) else (
    for /f "tokens=*" %%i in ('python --version') do set PYTHON_VERSION=%%i
    echo %GREEN%[SUCCESS]%NC% Python is available: %PYTHON_VERSION%
)

REM Create logs directory
if not exist "logs" mkdir logs
echo %GREEN%[SUCCESS]%NC% Logs directory ready

REM Setup environment files
echo %BLUE%[INFO]%NC% Setting up environment configuration...
if not exist "backend\.env.local" (
    echo %BLUE%[INFO]%NC% Running environment setup...
    node scripts\setup-with-available-keys.js
    if %errorlevel% neq 0 (
        echo %RED%[ERROR]%NC% Environment setup failed
        pause
        exit /b 1
    )
)
echo %GREEN%[SUCCESS]%NC% Environment configuration ready

REM Install backend dependencies
echo %BLUE%[INFO]%NC% Installing backend dependencies...
cd backend
if not exist "node_modules" (
    echo Installing backend packages...
    npm install
    if %errorlevel% neq 0 (
        echo %RED%[ERROR]%NC% Backend dependency installation failed
        cd ..
        pause
        exit /b 1
    )
)

REM Setup database
echo %BLUE%[INFO]%NC% Setting up database...
if exist "prisma\schema.prisma" (
    npx prisma generate
    npx prisma db push
)
cd ..
echo %GREEN%[SUCCESS]%NC% Backend setup complete

REM Install LLM service dependencies
echo %BLUE%[INFO]%NC% Setting up LLM service...
cd llm-service
if not exist "venv" (
    echo Creating Python virtual environment...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo %YELLOW%[WARNING]%NC% Failed to create virtual environment
        cd ..
        goto :skip_llm
    )
)

echo Activating virtual environment and installing packages...
call venv\Scripts\activate.bat
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo %YELLOW%[WARNING]%NC% LLM service setup had issues
)
cd ..
echo %GREEN%[SUCCESS]%NC% LLM service setup complete
goto :continue_setup

:skip_llm
echo %YELLOW%[WARNING]%NC% Skipping LLM service setup due to Python issues

:continue_setup
REM Install Telegram bot dependencies
echo %BLUE%[INFO]%NC% Setting up Telegram bot...
cd telegram-bot
if not exist "node_modules" (
    echo Installing Telegram bot packages...
    npm install
    if %errorlevel% neq 0 (
        echo %RED%[ERROR]%NC% Telegram bot dependency installation failed
        cd ..
        pause
        exit /b 1
    )
)
cd ..
echo %GREEN%[SUCCESS]%NC% Telegram bot setup complete

REM Install frontend dependencies (optional)
echo %BLUE%[INFO]%NC% Setting up frontend (optional)...
if exist "frontend" (
    cd frontend
    if not exist "node_modules" (
        echo Installing frontend packages...
        npm install
        if %errorlevel% neq 0 (
            echo %YELLOW%[WARNING]%NC% Frontend setup had issues
        )
    )
    cd ..
    echo %GREEN%[SUCCESS]%NC% Frontend setup complete
) else (
    echo %YELLOW%[WARNING]%NC% Frontend directory not found, skipping...
)

echo.
echo %CYAN%🎉 Setup Complete! Starting Services...%NC%
echo.

REM Start Backend API
echo %BLUE%[INFO]%NC% Starting Backend API with Complete Automation...
cd backend
start "Backend API" cmd /k "npm run dev"
timeout /t 5 /nobreak >nul
cd ..
echo %GREEN%[SUCCESS]%NC% Backend API started

REM Start LLM Service
echo %BLUE%[INFO]%NC% Starting Enhanced LLM Service...
if exist "llm-service\venv" (
    cd llm-service
    start "LLM Service" cmd /k "venv\Scripts\activate.bat && python app.py"
    timeout /t 5 /nobreak >nul
    cd ..
    echo %GREEN%[SUCCESS]%NC% LLM Service started
) else (
    echo %YELLOW%[WARNING]%NC% LLM Service not available
)

REM Start Telegram Bot
echo %BLUE%[INFO]%NC% Starting Comprehensive Telegram Bot...
cd telegram-bot
start "Telegram Bot" cmd /k "npm run dev"
timeout /t 5 /nobreak >nul
cd ..
echo %GREEN%[SUCCESS]%NC% Telegram Bot started

REM Start Frontend (optional)
if exist "frontend" (
    echo %BLUE%[INFO]%NC% Starting Frontend Dashboard...
    cd frontend
    start "Frontend" cmd /k "npm run dev"
    timeout /t 3 /nobreak >nul
    cd ..
    echo %GREEN%[SUCCESS]%NC% Frontend started
)

echo.
echo %CYAN%🎉 X Marketing Platform - Complete Automation Suite Deployed!%NC%
echo %CYAN%================================================================%NC%
echo.
echo %GREEN%🌐 Available Services:%NC%
echo    • Backend API: %BLUE%http://localhost:3001%NC%
echo    • API Documentation: %BLUE%http://localhost:3001/api-docs%NC%
echo    • LLM Service: %BLUE%http://localhost:3003%NC%
echo    • Telegram Bot: %GREEN%Active and listening%NC%
if exist "frontend" (
    echo    • Frontend Dashboard: %BLUE%http://localhost:3000%NC%
)
echo.
echo %GREEN%🤖 Complete Automation Suite:%NC%
echo    • AI Content Generation: %GREEN%✅ Active%NC%
echo    • Automated Posting: %GREEN%✅ Active%NC%
echo    • Automated Liking: %GREEN%✅ Active%NC%
echo    • Automated Commenting: %GREEN%✅ Active%NC%
echo    • Automated Retweeting: %GREEN%✅ Active%NC%
echo    • Automated Following: %GREEN%✅ Active%NC%
echo    • Automated DM: %GREEN%✅ Active%NC%
echo    • Poll Voting: %GREEN%✅ Active%NC%
echo    • Thread Management: %GREEN%✅ Active%NC%
echo    • Quality Control: %GREEN%✅ Active%NC%
echo    • Compliance Monitoring: %GREEN%✅ Active%NC%
echo    • Multi-Account Management: %GREEN%✅ Active%NC%
echo.
echo %GREEN%🔑 API Credentials Configured:%NC%
echo    • Telegram Bot: %GREEN%✅ **********:AAFm6v8KPzn1zPZmHKklXjkIwzQ8fYY25O0%NC%
echo    • Hugging Face: %GREEN%✅ *************************************%NC%
echo    • X/Twitter API: %YELLOW%⚠️ Add your credentials to environment files%NC%
echo.
echo %YELLOW%📱 Comprehensive Telegram Bot Control:%NC%
echo    1. Message your bot on Telegram
echo    2. Send: %BLUE%/start%NC% to initialize
echo    3. Use: %BLUE%/help%NC% for all 50+ commands
echo    4. Content: %BLUE%/generate ^<topic^>%NC% for AI content
echo    5. Automation: %BLUE%/automation%NC% for full control
echo    6. Liking: %BLUE%/like_automation%NC% for like automation
echo    7. Commenting: %BLUE%/comment_automation%NC% for comment automation
echo    8. Following: %BLUE%/follow_automation%NC% for follow automation
echo    9. Analytics: %BLUE%/dashboard%NC% for real-time analytics
echo    10. Emergency: %BLUE%/emergency_stop%NC% for immediate halt
echo.
echo %YELLOW%🔧 Next Steps:%NC%
echo    1. Add your X/Twitter API credentials to the environment files
echo    2. Message your Telegram bot to start using the platform
echo    3. Use the web dashboard at http://localhost:3000
echo    4. Monitor logs in the opened command windows
echo.
echo %GREEN%✅ All services are running in separate windows%NC%
echo %GREEN%✅ Platform is ready for complete X/Twitter automation%NC%
echo.
echo %CYAN%Press any key to open the web dashboard...%NC%
pause >nul
start http://localhost:3000

echo.
echo %GREEN%🎯 Deployment Complete! Your X Marketing Platform is ready!%NC%
echo.
pause
