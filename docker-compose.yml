version: '3.8'

services:
  # Database
  postgres:
    image: postgres:15-alpine
    container_name: x-marketing-postgres
    environment:
      POSTGRES_DB: x_marketing
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - x-marketing-network

  # Redis for caching and queues
  redis:
    image: redis:7-alpine
    container_name: x-marketing-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - x-marketing-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: x-marketing-backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/x_marketing
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - x-marketing-network

  # Frontend Dashboard
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: x-marketing-frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:3001
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - x-marketing-network

  # Telegram Bot
  telegram-bot:
    build:
      context: ./telegram-bot
      dockerfile: Dockerfile
    container_name: x-marketing-telegram
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - BACKEND_URL=http://backend:3001
    depends_on:
      - backend
    volumes:
      - ./telegram-bot:/app
      - /app/node_modules
    networks:
      - x-marketing-network

  # LLM Service
  llm-service:
    build:
      context: ./llm-service
      dockerfile: Dockerfile
    container_name: x-marketing-llm
    ports:
      - "3003:3003"
    environment:
      - FLASK_ENV=production
      - OLLAMA_HOST=http://ollama:11434
    depends_on:
      - ollama
    volumes:
      - ./llm-service:/app
    networks:
      - x-marketing-network

  # Ollama for local LLM
  ollama:
    image: ollama/ollama:latest
    container_name: x-marketing-ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - x-marketing-network

  # Automation Engine
  automation-engine:
    build:
      context: ./automation-engine
      dockerfile: Dockerfile
    container_name: x-marketing-automation
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/x_marketing
      - REDIS_URL=redis://redis:6379
      - BACKEND_URL=http://backend:3001
      - LLM_SERVICE_URL=http://llm-service:3003
    depends_on:
      - postgres
      - redis
      - backend
      - llm-service
    volumes:
      - ./automation-engine:/app
      - /app/node_modules
    networks:
      - x-marketing-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: x-marketing-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - x-marketing-network

volumes:
  postgres_data:
  redis_data:
  ollama_data:

networks:
  x-marketing-network:
    driver: bridge
