
# This file was generated by 'versioneer.py' (0.29) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-03-14T19:18:49-0700",
 "dirty": false,
 "error": null,
 "full-revisionid": "8b111eb5f0298e5b095272027bf3194d2c999aa8",
 "version": "3.12"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
