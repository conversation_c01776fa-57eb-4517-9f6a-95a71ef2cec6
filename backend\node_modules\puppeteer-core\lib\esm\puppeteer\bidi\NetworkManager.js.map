{"version": 3, "file": "NetworkManager.js", "sourceRoot": "", "sources": ["../../../../src/bidi/NetworkManager.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH,OAAO,EAAC,YAAY,EAAE,iBAAiB,EAAC,MAAM,2BAA2B,CAAC;AAC1E,OAAO,EACL,mBAAmB,GAEpB,MAAM,mCAAmC,CAAC;AAC3C,OAAO,EAAC,eAAe,EAAC,MAAM,uBAAuB,CAAC;AAItD,OAAO,EAAC,eAAe,EAAC,MAAM,kBAAkB,CAAC;AACjD,OAAO,EAAC,gBAAgB,EAAC,MAAM,mBAAmB,CAAC;AAGnD;;GAEG;AACH,MAAM,OAAO,kBAAmB,SAAQ,YAAkC;IACxE,WAAW,CAAiB;IAC5B,KAAK,CAAW;IAChB,cAAc,GAAG,IAAI,eAAe,EAAE,CAAC;IAEvC,WAAW,GAAG,IAAI,GAAG,EAA2B,CAAC;IACjD,cAAc,GAAG,IAAI,GAAG,EAA4B,CAAC;IAErD,YAAY,UAA0B,EAAE,IAAc;QACpD,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAElB,4CAA4C;QAC5C,IAAI,CAAC,cAAc,CAAC,GAAG,CACrB,IAAI,iBAAiB,CACnB,IAAI,CAAC,WAAW,EAChB,2BAA2B,EAC3B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CACrC,CACF,CAAC;QACF,IAAI,CAAC,cAAc,CAAC,GAAG,CACrB,IAAI,iBAAiB,CACnB,IAAI,CAAC,WAAW,EAChB,yBAAyB,EACzB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CACnC,CACF,CAAC;QACF,IAAI,CAAC,cAAc,CAAC,GAAG,CACrB,IAAI,iBAAiB,CACnB,IAAI,CAAC,WAAW,EAChB,2BAA2B,EAC3B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CACrC,CACF,CAAC;QACF,IAAI,CAAC,cAAc,CAAC,GAAG,CACrB,IAAI,iBAAiB,CACnB,IAAI,CAAC,WAAW,EAChB,oBAAoB,EACpB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAC9B,CACF,CAAC;IACJ,CAAC;IAED,oBAAoB,CAAC,KAA+C;QAClE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;QACpD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC5D,IAAI,aAA8B,CAAC;QACnC,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,aAAa,GAAG,IAAI,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;QAC5E,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,IAAI,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QAC3D,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;IACxD,CAAC;IAED,kBAAkB,CAAC,MAA8C,IAAG,CAAC;IAErE,oBAAoB,CAAC,KAA+C;QAClE,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC5D,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO;QACT,CAAC;QACD,MAAM,QAAQ,GAAG,IAAI,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtD,OAAO,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC7B,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;YACrB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QACtD,CAAC;QACD,IAAI,QAAQ,CAAC,SAAS,EAAE,EAAE,CAAC;YACzB,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;QACjE,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAClD,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;IAC1D,CAAC;IAED,aAAa,CAAC,KAAwC;QACpD,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC5D,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO;QACT,CAAC;QACD,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC;QACvC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACjD,CAAC;IAED,qBAAqB,CAAC,YAA4B;QAChD,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAEvD,OAAO,QAAQ,IAAI,IAAI,CAAC;IAC1B,CAAC;IAED,qBAAqB;QACnB,IAAI,sBAAsB,GAAG,CAAC,CAAC;QAC/B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC;YAChD,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;gBAChD,sBAAsB,EAAE,CAAC;YAC3B,CAAC;QACH,CAAC;QAED,OAAO,sBAAsB,CAAC;IAChC,CAAC;IAED,yBAAyB,CAAC,KAAgB;QACxC,KAAK,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;YACvD,IAAI,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,EAAE,CAAC;gBAC9B,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;QAED,KAAK,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YAC3D,IAAI,QAAQ,CAAC,KAAK,EAAE,KAAK,KAAK,EAAE,CAAC;gBAC/B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACjC,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO;QACL,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;IAChC,CAAC;CACF"}