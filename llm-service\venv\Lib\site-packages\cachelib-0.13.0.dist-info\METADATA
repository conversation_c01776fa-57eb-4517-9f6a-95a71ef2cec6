Metadata-Version: 2.1
Name: cachelib
Version: 0.13.0
Summary: A collection of cache libraries in the same API interface.
Home-page: https://github.com/pallets-eco/cachelib/
Maintainer: Pallets
Maintainer-email: <EMAIL>
License: BSD-3-Clause
Project-URL: Donate, https://palletsprojects.com/donate
Project-URL: Documentation, https://cachelib.readthedocs.io/
Project-URL: Changes, https://cachelib.readthedocs.io/changes/
Project-URL: Source Code, https://github.com/pallets-eco/cachelib/
Project-URL: Issue Tracker, https://github.com/pallets-eco/cachelib/issues/
Project-URL: Twitter, https://twitter.com/PalletsTeam
Project-URL: Chat, https://discord.gg/pallets
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE.rst

CacheLib
========

A collection of cache libraries in the same API interface. Extracted
from Werkzeug.


Installing
----------

Install and update using `pip`_:

.. code-block:: text

    $ pip install -U cachelib

.. _pip: https://pip.pypa.io/en/stable/getting-started/


Donate
------

The Pallets organization develops and supports Flask and the libraries
it uses. In order to grow the community of contributors and users, and
allow the maintainers to devote more time to the projects, `please
donate today`_.

.. _please donate today: https://palletsprojects.com/donate


Links
-----

-   Documentation: https://cachelib.readthedocs.io/
-   Changes: https://cachelib.readthedocs.io/changes/
-   PyPI Releases: https://pypi.org/project/cachelib/
-   Source Code: https://github.com/pallets/cachelib/
-   Issue Tracker: https://github.com/pallets/cachelib/issues/
-   Twitter: https://twitter.com/PalletsTeam
-   Chat: https://discord.gg/pallets
