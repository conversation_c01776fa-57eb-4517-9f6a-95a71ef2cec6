{"version": 3, "file": "puppeteer-core.js", "sourceRoot": "", "sources": ["../../../src/puppeteer-core.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;AAIH,+CAA6B;AAC7B,+CAA6B;AAC7B,qDAAmC;AACnC,iDAA+B;AAC/B,iDAA+B;AAC/B,iDAA+B;AAE/B;;GAEG;AACH,iEAA+C;AAE/C,8DAAsD;AAEtD;;GAEG;AACH,MAAM,SAAS,GAAG,IAAI,gCAAa,CAAC;IAClC,eAAe,EAAE,IAAI;CACtB,CAAC,CAAC;AAGD;;GAEG;AACH,eAAO,GAaL,SAAS;AAZX;;GAEG;AACH,mBAAW,GAST,SAAS;AARX;;GAEG;AACH,sBAAc,GAKZ,SAAS;AAJX;;GAEG;AACH,cAAM,GACJ,SAAS,QAAC;AAEd,kBAAe,SAAS,CAAC"}