
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  username: 'username',
  password: 'password',
  role: 'role',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserSessionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  refreshToken: 'refreshToken',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt'
};

exports.Prisma.UserActivityScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  action: 'action',
  details: 'details',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  createdAt: 'createdAt'
};

exports.Prisma.XAccountScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  username: 'username',
  displayName: 'displayName',
  email: 'email',
  phone: 'phone',
  accessToken: 'accessToken',
  accessTokenSecret: 'accessTokenSecret',
  accountId: 'accountId',
  isActive: 'isActive',
  isVerified: 'isVerified',
  isSuspended: 'isSuspended',
  suspensionReason: 'suspensionReason',
  proxyId: 'proxyId',
  fingerprintId: 'fingerprintId',
  lastActivity: 'lastActivity',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  followersCount: 'followersCount',
  followingCount: 'followingCount',
  tweetsCount: 'tweetsCount',
  likesCount: 'likesCount'
};

exports.Prisma.ProxyScalarFieldEnum = {
  id: 'id',
  host: 'host',
  port: 'port',
  username: 'username',
  password: 'password',
  type: 'type',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FingerprintScalarFieldEnum = {
  id: 'id',
  userAgent: 'userAgent',
  viewport: 'viewport',
  timezone: 'timezone',
  language: 'language',
  platform: 'platform',
  webgl: 'webgl',
  canvas: 'canvas',
  createdAt: 'createdAt'
};

exports.Prisma.CampaignScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  name: 'name',
  description: 'description',
  status: 'status',
  startDate: 'startDate',
  endDate: 'endDate',
  settings: 'settings',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AutomationScalarFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  campaignId: 'campaignId',
  type: 'type',
  status: 'status',
  config: 'config',
  schedule: 'schedule',
  lastRun: 'lastRun',
  nextRun: 'nextRun',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AutomationLogScalarFieldEnum = {
  id: 'id',
  automationId: 'automationId',
  status: 'status',
  message: 'message',
  details: 'details',
  executedAt: 'executedAt'
};

exports.Prisma.PostScalarFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  campaignId: 'campaignId',
  content: 'content',
  mediaUrls: 'mediaUrls',
  hashtags: 'hashtags',
  mentions: 'mentions',
  status: 'status',
  tweetId: 'tweetId',
  scheduledFor: 'scheduledFor',
  publishedAt: 'publishedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  likesCount: 'likesCount',
  retweetsCount: 'retweetsCount',
  repliesCount: 'repliesCount',
  viewsCount: 'viewsCount'
};

exports.Prisma.EngagementScalarFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  type: 'type',
  targetId: 'targetId',
  targetType: 'targetType',
  status: 'status',
  createdAt: 'createdAt'
};

exports.Prisma.AnalyticsScalarFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  postId: 'postId',
  date: 'date',
  metrics: 'metrics',
  createdAt: 'createdAt'
};

exports.Prisma.ApiKeyScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  name: 'name',
  key: 'key',
  isActive: 'isActive',
  lastUsed: 'lastUsed',
  createdAt: 'createdAt',
  expiresAt: 'expiresAt'
};

exports.Prisma.ContentTemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  category: 'category',
  template: 'template',
  variables: 'variables',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TrendingHashtagScalarFieldEnum = {
  id: 'id',
  hashtag: 'hashtag',
  volume: 'volume',
  category: 'category',
  sentiment: 'sentiment',
  updatedAt: 'updatedAt',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.UserRole = exports.$Enums.UserRole = {
  USER: 'USER',
  ADMIN: 'ADMIN',
  SUPER_ADMIN: 'SUPER_ADMIN'
};

exports.CampaignStatus = exports.$Enums.CampaignStatus = {
  DRAFT: 'DRAFT',
  ACTIVE: 'ACTIVE',
  PAUSED: 'PAUSED',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

exports.AutomationType = exports.$Enums.AutomationType = {
  POST_CONTENT: 'POST_CONTENT',
  AUTO_FOLLOW: 'AUTO_FOLLOW',
  AUTO_UNFOLLOW: 'AUTO_UNFOLLOW',
  AUTO_LIKE: 'AUTO_LIKE',
  AUTO_RETWEET: 'AUTO_RETWEET',
  AUTO_REPLY: 'AUTO_REPLY',
  ENGAGEMENT_BOOST: 'ENGAGEMENT_BOOST'
};

exports.AutomationStatus = exports.$Enums.AutomationStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PAUSED: 'PAUSED',
  ERROR: 'ERROR'
};

exports.PostStatus = exports.$Enums.PostStatus = {
  DRAFT: 'DRAFT',
  SCHEDULED: 'SCHEDULED',
  PUBLISHED: 'PUBLISHED',
  FAILED: 'FAILED',
  DELETED: 'DELETED'
};

exports.EngagementType = exports.$Enums.EngagementType = {
  LIKE: 'LIKE',
  RETWEET: 'RETWEET',
  REPLY: 'REPLY',
  FOLLOW: 'FOLLOW',
  UNFOLLOW: 'UNFOLLOW',
  MENTION: 'MENTION'
};

exports.Prisma.ModelName = {
  User: 'User',
  UserSession: 'UserSession',
  UserActivity: 'UserActivity',
  XAccount: 'XAccount',
  Proxy: 'Proxy',
  Fingerprint: 'Fingerprint',
  Campaign: 'Campaign',
  Automation: 'Automation',
  AutomationLog: 'AutomationLog',
  Post: 'Post',
  Engagement: 'Engagement',
  Analytics: 'Analytics',
  ApiKey: 'ApiKey',
  ContentTemplate: 'ContentTemplate',
  TrendingHashtag: 'TrendingHashtag'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
